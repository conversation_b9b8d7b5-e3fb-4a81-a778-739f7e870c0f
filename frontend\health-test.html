<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Health Service Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .service { padding: 10px; margin: 5px 0; border-radius: 5px; }
        .up { background: #d4edda; border: 1px solid #c3e6cb; }
        .partial { background: #fff3cd; border: 1px solid #ffeaa7; }
        .down { background: #f8d7da; border: 1px solid #f5c6cb; }
        button { padding: 10px 20px; margin: 10px 0; cursor: pointer; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Health Service Test</h1>
    <button onclick="testHealthService()">Test Health Service</button>
    <div id="results"></div>

    <script>
        // Simplified health service (same as the one we created)
        const HEALTH_ENDPOINTS = {
            'API Gateway': 'http://localhost:8090/actuator/health',
            'Eureka Server': 'http://localhost:8761/actuator/health',
            'Authentication': 'http://localhost:8080/actuator/health',
            'Flights': 'http://localhost:8081/actuator/health',
            'Passengers': 'http://localhost:8082/actuator/health',
            'User Management': 'http://localhost:8083/actuator/health',
            'Service Management': 'http://localhost:8084/actuator/health',
            'Travel History': 'http://localhost:8085/actuator/health'
        };

        const SIMPLE_ENDPOINTS = {
            'API Gateway': 'http://localhost:8090',
            'Eureka Server': 'http://localhost:8761',
            'Authentication': 'http://localhost:8080',
            'Flights': 'http://localhost:8081',
            'Passengers': 'http://localhost:8082',
            'User Management': 'http://localhost:8083',
            'Service Management': 'http://localhost:8084',
            'Travel History': 'http://localhost:8085'
        };

        async function checkServiceHealth(serviceName, healthUrl) {
            const startTime = Date.now();
            
            try {
                const response = await fetch(healthUrl, {
                    method: 'GET',
                    timeout: 5000,
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const responseTime = Date.now() - startTime;
                
                if (response.ok) {
                    const data = await response.json();
                    return {
                        name: serviceName,
                        status: data.status === 'UP' ? 'UP' : 'DOWN',
                        responseTime,
                        details: data,
                        endpoint: 'health'
                    };
                } else if (response.status === 404) {
                    return await checkSimpleEndpoint(serviceName, startTime);
                } else {
                    return {
                        name: serviceName,
                        status: 'PARTIAL',
                        responseTime,
                        error: `HTTP ${response.status}: ${response.statusText}`,
                        endpoint: 'health'
                    };
                }
            } catch (error) {
                return await checkSimpleEndpoint(serviceName, startTime);
            }
        }

        async function checkSimpleEndpoint(serviceName, startTime) {
            const simpleUrl = SIMPLE_ENDPOINTS[serviceName];
            
            try {
                const response = await fetch(simpleUrl, {
                    method: 'GET',
                    timeout: 5000
                });
                
                const responseTime = Date.now() - startTime;
                
                if (response.status < 500) {
                    return {
                        name: serviceName,
                        status: 'UP',
                        responseTime,
                        details: { status: 'Service responding' },
                        endpoint: 'simple'
                    };
                } else {
                    return {
                        name: serviceName,
                        status: 'PARTIAL',
                        responseTime,
                        error: `HTTP ${response.status}: ${response.statusText}`,
                        endpoint: 'simple'
                    };
                }
            } catch (error) {
                return {
                    name: serviceName,
                    status: 'DOWN',
                    responseTime: Date.now() - startTime,
                    error: error.message,
                    endpoint: 'simple'
                };
            }
        }

        async function testHealthService() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Checking services...</p>';

            const healthChecks = Object.entries(HEALTH_ENDPOINTS).map(([name, url]) =>
                checkServiceHealth(name, url)
            );
            
            const results = await Promise.all(healthChecks);
            
            // Calculate stats
            const upServices = results.filter(r => r.status === 'UP').length;
            const partialServices = results.filter(r => r.status === 'PARTIAL').length;
            const totalServices = results.length;
            const upPercentage = Math.round((upServices / totalServices) * 100);
            
            let html = `
                <h2>System Status</h2>
                <p><strong>Overall:</strong> ${upServices}/${totalServices} services UP (${upPercentage}%)</p>
                <p><strong>Partial:</strong> ${partialServices} services</p>
                <hr>
                <h3>Service Details:</h3>
            `;
            
            results.forEach(service => {
                const statusClass = service.status.toLowerCase();
                html += `
                    <div class="service ${statusClass}">
                        <strong>${service.name}</strong> - ${service.status}
                        <br>Response Time: ${service.responseTime}ms
                        <br>Check Type: ${service.endpoint}
                        ${service.error ? `<br>Error: ${service.error}` : ''}
                    </div>
                `;
            });
            
            html += `
                <hr>
                <h3>Raw Results:</h3>
                <pre>${JSON.stringify(results, null, 2)}</pre>
            `;
            
            resultsDiv.innerHTML = html;
        }
    </script>
</body>
</html>
